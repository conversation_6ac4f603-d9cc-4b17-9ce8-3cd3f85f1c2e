from unittest.mock import AsyncMock, patch
from uuid import uuid4

from fastapi import status
import pytest

from constants.message import (
    ALL_REQUIRED_FIELDS_CONFIRMED,
    NEED_INFO_INITIAL_PROMPT,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
)
from constants.operation_ids import operation_ids
from core.urls import URLResolver
from dependencies import CustomAsyncClient
from schemas import AggregatedData
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import SystemMessageSerializer, UserMessageSerializer
from schemas.quals_clients import ClientSearchResponse


class TestGetLastMessageRefactored:
    """Test suite for the refactored GET last message endpoint with system message generation."""

    @pytest.fixture
    def user_message_data(self, test_conversation_id):
        """Create sample user message data."""
        return {
            'id': uuid4(),
            'conversation_id': test_conversation_id,
            'role': MessageRole.USER,
            'type': MessageType.TEXT,
            'content': 'Test user message',
            'created_at': '2024-01-01T00:00:00Z',
            'selected_option': None,
        }

    @pytest.fixture
    def system_message_data(self, test_conversation_id):
        """Create sample system message data."""
        return {
            'id': uuid4(),
            'conversation_id': test_conversation_id,
            'role': MessageRole.SYSTEM,
            'type': MessageType.TEXT,
            'content': 'Test system message',
            'created_at': '2024-01-01T00:00:00Z',
            'options': '[]',
            'suggested_prompts': '[]',
        }

    @pytest.fixture
    def sample_aggregated_data(self):
        """Create sample aggregated data for testing."""
        return AggregatedData(
            client_name=['Test Client Inc.'],
            ldmf_country=['United States'],
            date_intervals=[('2024-01-01', '2024-12-31')],
            objective_and_scope='Test objective and scope content',
            outcomes='Test outcomes content',
        )

    @pytest.fixture
    def empty_aggregated_data(self):
        """Create empty aggregated data for testing."""
        return AggregatedData()

    async def test_get_last_message_system_message_exists(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        system_message_data,
    ):
        """Test getting last message when it's already a system message."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the message service to return a system message
        with patch('services.conversation_message.ConversationMessageService.get_last') as mock_get_last:
            mock_get_last.return_value = SystemMessageSerializer.model_validate(system_message_data)

            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == 'Test system message'

    async def test_get_last_message_user_message_with_extracted_data(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        user_message_data,
        sample_aggregated_data,
    ):
        """Test getting last message when it's a user message and extracted data exists."""
        # post new user message
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
        ):
            # Set up mocks for first message
            mock_conversation = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': 'collecting_client_name'}
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        assert 'system' in data
        assert data['system'] is None

        # test get last
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services
        with (
            patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data,
        ):
            # Setup extracted data service mock
            mock_aggregate_data.return_value = sample_aggregated_data
            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == ALL_REQUIRED_FIELDS_CONFIRMED

        # Verify the services were called correctly
        mock_aggregate_data.assert_called_once_with(test_conversation_id)

    async def test_get_last_message_user_message_no_extracted_data(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        empty_aggregated_data,
    ):
        """Test getting last message when it's a user message but no extracted data exists."""
        # post new user message
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
        ):
            # Set up mocks for first message
            mock_conversation = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': 'collecting_client_name'}
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        assert 'system' in data
        assert data['system'] is None

        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services
        with (
            patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data,
        ):
            # Setup extracted data service mock (returns empty data)
            mock_aggregate_data.return_value = empty_aggregated_data

            response = await async_client.get(url, headers=auth_header)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['role'] == MessageRole.SYSTEM
        assert data['content'] == NEED_INFO_INITIAL_PROMPT

        # Verify extracted data service was called but no system message was created
        mock_aggregate_data.assert_called_once_with(test_conversation_id)

    async def test_get_last_message_user_message_text_type(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        sample_aggregated_data,
    ):
        """Test getting last message when it's a user message with file type."""
        # post new user message
        user_message = 'I need help with my qual for NewTech Solutions'

        with (
            patch(
                'services.conversation_message.ConversationMessageProcessor._get_intent',
                new_callable=AsyncMock,
                return_value=ConversationMessageIntention.EXTRACTION,
            ),
            patch('repositories.conversation.ConversationRepository.get') as mock_get_conversation,
            patch('repositories.conversation.ConversationRepository.get_confirmed_data') as mock_get_confirmed_data,
            patch('repositories.quals_clients.QualsClientsRepository.search_clients') as mock_search,
        ):
            # Set up mocks for first message
            mock_conversation = type(
                'MockConversation', (), {'id': test_conversation_id, 'State': 'collecting_client_name'}
            )
            mock_get_conversation.return_value = mock_conversation
            mock_get_confirmed_data.return_value = ConfirmedData()

            # Mock no client search results
            mock_search.return_value = ClientSearchResponse(
                clients=[],
                total_count=0,
                page_size=5,
                page_idx=0,
            )

            message_url = url_resolver.reverse(operation_ids.message.CREATE)
            message_data = {
                'conversation_id': str(test_conversation_id),
                'role': MessageRole.USER,
                'message_type': MessageType.TEXT,
                'content': user_message,
            }

            message_response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert message_response.status_code == status.HTTP_201_CREATED
        data = message_response.json()

        assert 'system' in data
        assert data['system'] is None

        # Test last message with mocked aggregated data
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services
        with patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data:
            # Setup other service mocks
            mock_aggregate_data.return_value = sample_aggregated_data

            response = await async_client.get(url, headers=auth_header)

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data['role'] == MessageRole.SYSTEM
            assert data['type'] == MessageType.TEXT
            assert data['content'] == ALL_REQUIRED_FIELDS_CONFIRMED

    async def test_get_last_message_system_generation_error(
        self,
        async_client: CustomAsyncClient,
        url_resolver: URLResolver,
        auth_mock,
        auth_header,
        test_conversation_id,
        user_message_data,
    ):
        """Test getting last message when system message generation fails."""
        url = url_resolver.reverse(operation_ids.message.GET_LAST, conversation_id=test_conversation_id)

        # Mock the services with error in system message generation
        with (
            patch('services.conversation_message.ConversationMessageService.get_last') as mock_get_last,
            patch('services.extracted_data.ExtractedDataService.aggregate_data') as mock_aggregate_data,
        ):
            # Setup message service mock
            mock_get_last.return_value = UserMessageSerializer.model_validate(user_message_data)

            # Setup extracted data service mock to raise an error
            mock_aggregate_data.side_effect = Exception('Test error')

            response = await async_client.get(url, headers=auth_header)

        # Should fall back to returning the original user message
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['role'] == MessageRole.USER
        assert data['content'] == 'Test user message'
